package maps

import (
	"bfmap/dbproto"
	"testing"
	"time"
)

// BenchmarkLanguageCompatibilityCheck 测试语言兼容性检查的性能
func BenchmarkLanguageCompatibilityCheck(b *testing.B) {
	testCases := []struct {
		requestLang  string
		tokenLang    string
		providerType dbproto.MapProviderEnum
		mapType      string
	}{
		{"en-US", "en", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap},
		{"zh-CN", "zh", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap},
		{"fr-FR", "fr", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap},
		{"zh-CN", "zh-CN", dbproto.MapProviderEnum_ProviderTiandituLocalDirectory, MapTypeRoadmap},
		{"en-US", "", dbproto.MapProviderEnum_ProviderOSMLocalDirectory, MapTypeRoadmap},
		{"", "", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeSatellite},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, tc := range testCases {
			isLanguageCompatible(tc.requestLang, tc.tokenLang, tc.providerType, tc.mapType)
		}
	}
}

// BenchmarkLanguageCompatibilityCheckWithCache 测试带缓存的语言兼容性检查性能
func BenchmarkLanguageCompatibilityCheckWithCache(b *testing.B) {
	// 预热缓存
	testCases := []struct {
		requestLang  string
		tokenLang    string
		providerType dbproto.MapProviderEnum
		mapType      string
	}{
		{"en-US", "en", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap},
		{"zh-CN", "zh", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap},
		{"fr-FR", "fr", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap},
	}

	// 预热缓存
	for _, tc := range testCases {
		isLanguageCompatible(tc.requestLang, tc.tokenLang, tc.providerType, tc.mapType)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, tc := range testCases {
			isLanguageCompatible(tc.requestLang, tc.tokenLang, tc.providerType, tc.mapType)
		}
	}
}

// BenchmarkFilePathBuilding 测试文件路径构建性能
func BenchmarkFilePathBuilding(b *testing.B) {
	token := &MapProviderToken{
		BaseUrl: "/data/maps/google",
	}
	mapReq := &MapReq{
		X:       12345,
		Y:       67890,
		Z:       15,
		MapType: MapTypeRoadmap,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		buildLocalFilePath(token, mapReq)
	}
}

// BenchmarkFilterCompatibleTokens 测试token过滤性能
func BenchmarkFilterCompatibleTokens(b *testing.B) {
	tokens := []*MapProviderToken{
		{
			TokenRid: "token1",
			Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			Language: "en-US",
			Status:   1,
			MinZoom:  1,
			MaxZoom:  20,
		},
		{
			TokenRid: "token2",
			Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			Language: "zh-CN",
			Status:   1,
			MinZoom:  1,
			MaxZoom:  20,
		},
		{
			TokenRid: "token3",
			Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			Language: "fr-FR",
			Status:   1,
			MinZoom:  1,
			MaxZoom:  20,
		},
	}

	mapReq := &MapReq{
		Lang:    "en-US",
		Z:       10,
		MapType: MapTypeRoadmap,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		filterCompatibleTokens(tokens, mapReq)
	}
}

// TestLanguageMatchingPerformance 测试语言匹配的性能特征
func TestLanguageMatchingPerformance(t *testing.T) {
	// 测试少量语言组合的性能
	languages := []string{"en-US", "zh-CN", "fr-FR", "de-DE"}

	start := time.Now()
	iterations := 100

	for i := 0; i < iterations; i++ {
		for _, reqLang := range languages {
			for _, tokLang := range languages {
				isLanguageCompatible(
					reqLang,
					tokLang,
					dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
					MapTypeRoadmap,
				)
			}
		}
	}

	duration := time.Since(start)
	totalChecks := iterations * len(languages) * len(languages)
	avgTimePerCheck := duration / time.Duration(totalChecks)

	t.Logf("Performed %d language compatibility checks in %v", totalChecks, duration)
	t.Logf("Average time per check: %v", avgTimePerCheck)

	// 确保平均时间在合理范围内（小于10微秒）
	if avgTimePerCheck > 10*time.Microsecond {
		t.Errorf("Language compatibility check is too slow: %v per check", avgTimePerCheck)
	}
}

// TestCacheEffectiveness 测试缓存的有效性
func TestCacheEffectiveness(t *testing.T) {
	// 清空缓存
	langMatchCache.mu.Lock()
	langMatchCache.cache = make(map[string]bool)
	langMatchCache.mu.Unlock()

	// 第一次调用（缓存未命中）
	start1 := time.Now()
	for i := 0; i < 1000; i++ {
		isLanguageCompatible("en-US", "en", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap)
	}
	duration1 := time.Since(start1)

	// 第二次调用（缓存命中）
	start2 := time.Now()
	for i := 0; i < 1000; i++ {
		isLanguageCompatible("en-US", "en", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap)
	}
	duration2 := time.Since(start2)

	t.Logf("First run (cache miss): %v", duration1)
	t.Logf("Second run (cache hit): %v", duration2)

	// 缓存命中应该明显更快
	if duration2 >= duration1 {
		t.Errorf("Cache is not effective: cache hit (%v) should be faster than cache miss (%v)", duration2, duration1)
	}

	// 缓存命中应该至少快50%
	if float64(duration2) > float64(duration1)*0.5 {
		t.Logf("Warning: Cache improvement is less than expected. Miss: %v, Hit: %v", duration1, duration2)
	}
}
