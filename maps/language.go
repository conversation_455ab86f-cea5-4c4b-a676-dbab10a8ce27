package maps

import (
	"bfmap/config"
	"bfmap/dbproto"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"golang.org/x/text/language"
)

// 语言匹配结果缓存
type languageMatchCache struct {
	mu    sync.RWMutex
	cache map[string]bool
	// 简单的TTL机制，避免缓存无限增长
	lastCleanup time.Time
}

var langMatchCache = &languageMatchCache{
	cache:       make(map[string]bool),
	lastCleanup: time.Now(),
}

// 生成缓存键
func makeCacheKey(requestLang, tokenLang string, providerType dbproto.MapProviderEnum, mapType string) string {
	return fmt.Sprintf("%s|%s|%d|%s", requestLang, tokenLang, int(providerType), mapType)
}

// 清理过期缓存（简单策略：每小时清理一次）
func (c *languageMatchCache) cleanup() {
	if time.Since(c.lastCleanup) > time.Hour {
		c.mu.Lock()
		c.cache = make(map[string]bool)
		c.lastCleanup = time.Now()
		c.mu.Unlock()
	}
}

// normalizeLanguageCode 标准化语言代码为BCP 47格式
func normalizeLanguageCode(lang string) (string, error) {
	if lang == "" {
		return "", nil
	}

	// 解析语言标签
	tag, err := language.Parse(lang)
	if err != nil {
		return "", fmt.Errorf("invalid language code: %w", err)
	}

	// 标准化为BCP 47格式
	return tag.String(), nil
}

// normalizeChinese 标准化中文语言代码
// 使用标准库处理中文语言变体，将各种中文变体归一化为标准格式
func normalizeChinese(lang string) string {
	// 先使用标准库解析语言标签
	tag, err := language.Parse(lang)
	if err != nil {
		// 如果解析失败，使用简单的映射作为fallback
		switch lang {
		case "zh", "zh_CN", "zh-CN":
			return "zh-CN"
		default:
			return lang
		}
	}

	// 对于中文，确保使用zh-CN格式
	if base, _ := tag.Base(); base.String() == "zh" {
		// 对于中文，统一返回zh-CN格式
		// 这里我们明确地构造zh-CN，而不依赖于原始输入的格式
		return "zh-CN"
	}

	// 非中文语言，返回标准化格式
	return tag.String()
}

// matchLanguage 执行语言匹配，支持精确匹配和语言族匹配
// 注意：requestLang应该已经在parseMapReq中标准化过
func matchLanguage(requestLang, tokenLang string) bool {
	// 标准化token语言代码（只标准化一次）
	tokLang, err := normalizeLanguageCode(tokenLang)
	if err != nil {
		if config.IsVerboseDebugMap {
			slog.Warn("failed to normalize token language code",
				"tokenLang", tokenLang,
				"err", err)
		}
		return false
	}

	// 精确匹配
	if requestLang == tokLang {
		return true
	}

	// 语言族匹配 - 避免重复解析已标准化的语言
	if requestLang == "" || tokLang == "" {
		return false
	}

	// 使用已标准化的语言代码进行解析
	reqTag, err := language.Parse(requestLang)
	if err != nil {
		if config.IsVerboseDebugMap {
			slog.Warn("failed to parse request language for family matching",
				"requestLang", requestLang,
				"err", err)
		}
		return false
	}

	tokTag, err := language.Parse(tokLang)
	if err != nil {
		if config.IsVerboseDebugMap {
			slog.Warn("failed to parse token language for family matching",
				"tokenLang", tokLang,
				"err", err)
		}
		return false
	}

	// 比较语言族（去除地区信息）
	reqBase, _ := reqTag.Base()
	tokBase, _ := tokTag.Base()

	return reqBase == tokBase
}

// matchTiandituLanguage 天地图特定的语言匹配逻辑
// 仅支持中文，使用标准库处理语言变体
func matchTiandituLanguage(requestLang, tokenLang string) bool {
	normalizedReqLang := normalizeChinese(requestLang)
	normalizedTokenLang := normalizeChinese(tokenLang)

	return normalizedReqLang == "zh-CN" && normalizedTokenLang == "zh-CN"
}

// isLanguageCompatible 检查请求语言与token语言是否兼容
// 根据不同的provider类型和地图类型执行相应的匹配逻辑
func isLanguageCompatible(requestLang, tokenLang string, providerType dbproto.MapProviderEnum, mapType string) bool {
	// 向后兼容：空token语言匹配所有请求
	if tokenLang == "" {
		return true
	}

	// 卫星图跳过语言检查，因为卫星图通常不包含文字
	if mapType == MapTypeSatellite {
		return true
	}

	// 检查缓存
	cacheKey := makeCacheKey(requestLang, tokenLang, providerType, mapType)
	langMatchCache.mu.RLock()
	if result, exists := langMatchCache.cache[cacheKey]; exists {
		langMatchCache.mu.RUnlock()
		return result
	}
	langMatchCache.mu.RUnlock()

	// 执行实际的语言匹配逻辑
	var result bool
	switch providerType {
	case dbproto.MapProviderEnum_ProviderGoogleLocalDirectory:
		// Google支持多语言匹配
		result = matchLanguage(requestLang, tokenLang)

	case dbproto.MapProviderEnum_ProviderTiandituLocalDirectory:
		// 天地图仅支持中文
		result = matchTiandituLanguage(requestLang, tokenLang)

	case dbproto.MapProviderEnum_ProviderOSMLocalDirectory:
		// OSM跳过语言匹配，语言由地理位置决定
		result = true

	default:
		// 未知provider类型，默认不兼容
		if config.IsVerboseDebugMap {
			slog.Warn("unknown provider type for language compatibility check",
				"providerType", providerType)
		}
		result = false
	}

	// 缓存结果
	langMatchCache.mu.Lock()
	langMatchCache.cache[cacheKey] = result
	// 只在缓存大小超过阈值时清理，避免频繁清理
	if len(langMatchCache.cache) > 1000 {
		langMatchCache.cleanup()
	}
	langMatchCache.mu.Unlock()

	return result
}
