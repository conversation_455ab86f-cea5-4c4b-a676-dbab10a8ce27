# 本地目录Fallback机制与语言支持功能设计文档

## 设计概述

本文档描述了本地目录fallback机制与语言支持功能的核心设计。该功能在现有地图服务架构基础上，添加本地目录fallback层和语言匹配机制，提升系统可靠性和国际化支持能力。

## 系统架构设计

### 核心处理流程

```mermaid
graph TD
    Client[客户端请求] --> Cache[缓存查询]
    Cache -->|成功| Response[返回瓦片]
    Cache -->|失败| LocalDir[本地目录Fallback]
    LocalDir -->|成功| Response
    LocalDir -->|失败| NATS[NATS请求]
    NATS -->|成功| Response
    NATS -->|失败| Online[在线Provider]
    Online --> Response
```

### 核心功能模块

#### 1. 语言匹配模块

- 语言代码标准化处理（使用BCP 47标准）
- 不同Provider的语言匹配规则实现
- 向后兼容性处理（空语言字段匹配所有请求）

#### 2. 本地目录访问模块

- Provider类型映射（在线Provider → 本地目录Provider）
- 本地文件路径构建和读取
- 文件访问错误处理

#### 3. Token过滤模块

- 基于语言兼容性过滤Provider Token
- Token有效性验证

## Provider类型映射

### 在线Provider与本地目录Provider对应关系

| 在线Provider | 本地目录Provider | 语言支持 |
|-------------|------------------|---------|
| ProviderGoogle (0) | ProviderGoogleLocalDirectory (3) | 多语言支持 |
| ProviderTianditu (1) | ProviderTiandituLocalDirectory (4) | 仅zh-CN |
| ProviderOSM (2) | ProviderOSMLocalDirectory (5) | 跳过语言检查 |

## 语言匹配规则

### 不同Provider的语言支持策略

**Google地图（ProviderGoogleLocalDirectory）**：

- 支持多语言匹配，需要严格的language字段匹配
- 卫星图层跳过语言检查
- 路线图/混合图层执行语言匹配

**天地图（ProviderTiandituLocalDirectory）**：

- 仅支持zh-CN，使用标准库进行语言变体处理
- 非中文语言请求时跳过该provider
- 卫星图层跳过语言检查

**OSM（ProviderOSMLocalDirectory）**：

- 跳过语言匹配检查，语言由地理位置自动决定
- 所有语言请求都可以使用同一套OSM瓦片

## 错误处理策略

### 主要错误类型及处理方式

**语言代码解析失败**：

- 记录警告日志，继续尝试其他可用的provider token
- 不中断整个请求流程

**本地文件不存在**：

- 记录调试级别日志，不视为错误
- 继续fallback到NATS或在线provider

**Token无效或过期**：

- 跳过当前Token，尝试下一个可用Token
- 记录相应的错误信息

## 实现细节

### 语言标准化实现

#### normalizeLanguageCode函数

```go
func normalizeLanguageCode(lang string) (string, error) {
    if lang == "" {
        return "", nil
    }

    // 解析语言标签
    tag, err := language.Parse(lang)
    if err != nil {
        return "", fmt.Errorf("invalid language code: %w", err)
    }

    // 标准化为BCP 47格式
    return tag.String(), nil
}
```

**设计说明**：

- 使用`golang.org/x/text/language`库进行语言代码标准化
- 支持BCP 47格式的语言标签
- 处理各种语言变体，依赖标准库的BCP 47格式处理
- 对于只有语言没有国家的语言标签（如`zh`），自动归一化到对应的BCP 47语言格式

### 语言匹配实现

#### isLanguageCompatible函数

```go
func isLanguageCompatible(requestLang, tokenLang string, providerType dbproto.MapProviderEnum, mapType string) bool {
    // 向后兼容：空token语言匹配所有请求
    if tokenLang == "" {
        return true
    }

    // 卫星图跳过语言检查
    if mapType == MapTypeSatellite {
        return true
    }

    // 根据provider类型执行特定匹配逻辑
    switch providerType {
    case dbproto.MapProviderEnum_ProviderGoogleLocalDirectory:
        return matchLanguage(requestLang, tokenLang)
    case dbproto.MapProviderEnum_ProviderTiandituLocalDirectory:
        return matchTiandituLanguage(requestLang, tokenLang)
    case dbproto.MapProviderEnum_ProviderOSMLocalDirectory:
        return true
    default:
        return false
    }
}
```

#### matchLanguage函数

```go
func matchLanguage(requestLang, tokenLang string) bool {
    // 标准化语言代码
    reqLang, err := normalizeLanguageCode(requestLang)
    if err != nil {
        return false
    }

    tokLang, err := normalizeLanguageCode(tokenLang)
    if err != nil {
        return false
    }

    // 精确匹配
    if reqLang == tokLang {
        return true
    }

    // 语言族匹配
    reqTag, err := language.Parse(reqLang)
    if err != nil {
        return false
    }

    tokTag, err := language.Parse(tokLang)
    if err != nil {
        return false
    }

    // 比较语言族（去除地区信息）
    reqBase, _ := reqTag.Base()
    tokBase, _ := tokTag.Base()

    return reqBase == tokBase
}
```

#### 语言标准化位置

**重要修正**：语言标准化在`parseMapReq`方法中进行，确保请求语言在解析阶段就被标准化为BCP 47格式：

```go
// 在parseMapReq方法中进行语言标准化
lang := r.URL.Query().Get("lang")

// 标准化语言代码为BCP 47格式
if lang != "" {
    normalizedLang, err := normalizeLanguageCode(lang)
    if err != nil {
        // 如果标准化失败，记录警告但继续处理
        if config.IsVerboseDebugMap {
            slog.Warn("failed to normalize language code, using original",
                "originalLang", lang,
                "err", err)
        }
    } else {
        lang = normalizedLang
    }
}
```

### Provider类型映射实现

#### getLocalDirectoryProviderType函数

```go
func getLocalDirectoryProviderType(onlineProviderType int) int {
    switch onlineProviderType {
    case int(dbproto.MapProviderEnum_ProviderGoogle):
        return int(dbproto.MapProviderEnum_ProviderGoogleLocalDirectory)
    case int(dbproto.MapProviderEnum_ProviderTianditu):
        return int(dbproto.MapProviderEnum_ProviderTiandituLocalDirectory)
    case int(dbproto.MapProviderEnum_ProviderOSM):
        return int(dbproto.MapProviderEnum_ProviderOSMLocalDirectory)
    default:
        return -1 // 不支持的provider类型
    }
}
```

### QueryTileFromLocalDirectory方法实现

```go
func QueryTileFromLocalDirectory(
    userRid string,
    mapReq *MapReq,
    localProviderType int,
    needCreateNewIndex bool,
) (tileInfo *TileInfo, imageBytes []byte, err error) {
    // 获取provider tokens后，添加语言匹配过滤
    providerTokens, err := GlobalMapsManager.GetMapProviderTokens(userRid, localProviderType)
    if err != nil {
        return nil, nil, err
    }

    // 过滤出语言兼容的tokens
    var compatibleTokens []*MapProviderToken
    for _, token := range providerTokens {
        if isLanguageCompatible(mapReq.Lang, token.Language, token.Provider, mapReq.MapType) {
            compatibleTokens = append(compatibleTokens, token)
        }
    }

    if len(compatibleTokens) == 0 {
        return nil, nil, errors.New("no language-compatible local directory provider token found")
    }

    // 尝试从兼容的tokens中读取文件
    for _, token := range compatibleTokens {
        imageBytes, err = readLocalDirectoryFile(token, mapReq)
        if err != nil {
            continue
        }

        // 成功读取文件，保存到数据库并返回
        tileInfo, err = SaveTileToDb(mapReq, imageBytes, needCreateNewIndex)
        return tileInfo, imageBytes, err
    }

    return nil, nil, fmt.Errorf("no local directory file found for request")
}
```

### ReqMapFromProviderWithToken方法扩展

```go
switch providerToken.Provider {
case dbproto.MapProviderEnum_ProviderGoogle:
    // ... 现有Google逻辑 ...
case dbproto.MapProviderEnum_ProviderTianditu:
    // ... 现有Tianditu逻辑 ...
case dbproto.MapProviderEnum_ProviderOSM:
    // ... 现有OSM逻辑 ...

// 新增本地目录provider支持
case dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
     dbproto.MapProviderEnum_ProviderTiandituLocalDirectory,
     dbproto.MapProviderEnum_ProviderOSMLocalDirectory:
    imageBytes, err = readLocalDirectoryFile(providerToken, mapReq)

default:
    return nil, nil, errors.New("not support provider")
}
```

## 总结

本设计文档描述了本地目录fallback机制与语言支持功能的核心设计，主要特点：

1. **简化的处理流程**：缓存 → 本地目录 → NATS → 在线Provider
2. **标准化语言处理**：使用BCP 47标准进行语言匹配
3. **Provider特定规则**：针对Google、天地图、OSM的不同语言支持策略
4. **完善的错误处理**：多层fallback机制确保服务可用性
