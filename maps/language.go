package maps

import (
	"bfmap/config"
	"bfmap/dbproto"
	"fmt"
	"log/slog"

	"golang.org/x/text/language"
)

// normalizeLanguageCode 标准化语言代码为BCP 47格式
func normalizeLanguageCode(lang string) (string, error) {
	if lang == "" {
		return "", nil
	}

	// 解析语言标签
	tag, err := language.Parse(lang)
	if err != nil {
		return "", fmt.Errorf("invalid language code: %w", err)
	}

	// 标准化为BCP 47格式
	return tag.String(), nil
}

// normalizeChinese 标准化中文语言代码
// 使用标准库处理中文语言变体，将各种中文变体归一化为标准格式
func normalizeChinese(lang string) string {
	// 先使用标准库解析语言标签
	tag, err := language.Parse(lang)
	if err != nil {
		// 如果解析失败，使用简单的映射作为fallback
		switch lang {
		case "zh", "zh_CN", "zh-CN":
			return "zh-CN"
		default:
			return lang
		}
	}

	// 对于中文，确保使用zh-CN格式
	if base, _ := tag.Base(); base.String() == "zh" {
		// 对于中文，统一返回zh-CN格式
		// 这里我们明确地构造zh-CN，而不依赖于原始输入的格式
		return "zh-CN"
	}

	// 非中文语言，返回标准化格式
	return tag.String()
}

// matchLanguage 执行语言匹配，支持精确匹配和语言族匹配
// 注意：requestLang应该已经在parseMapReq中标准化过
func matchLanguage(requestLang, tokenLang string) bool {
	// 标准化token语言代码
	tokLang, err := normalizeLanguageCode(tokenLang)
	if err != nil {
		if config.IsVerboseDebugMap {
			slog.Warn("failed to normalize token language code",
				"tokenLang", tokenLang,
				"err", err)
		}
		return false
	}

	// 精确匹配
	if requestLang == tokLang {
		return true
	}

	// 语言族匹配
	reqTag, err := language.Parse(requestLang)
	if err != nil {
		if config.IsVerboseDebugMap {
			slog.Warn("failed to parse request language for family matching",
				"requestLang", requestLang,
				"err", err)
		}
		return false
	}

	tokTag, err := language.Parse(tokLang)
	if err != nil {
		return false
	}

	// 比较语言族（去除地区信息）
	reqBase, _ := reqTag.Base()
	tokBase, _ := tokTag.Base()

	return reqBase == tokBase
}

// matchTiandituLanguage 天地图特定的语言匹配逻辑
// 仅支持中文，使用标准库处理语言变体
func matchTiandituLanguage(requestLang, tokenLang string) bool {
	normalizedReqLang := normalizeChinese(requestLang)
	normalizedTokenLang := normalizeChinese(tokenLang)

	return normalizedReqLang == "zh-CN" && normalizedTokenLang == "zh-CN"
}

// isLanguageCompatible 检查请求语言与token语言是否兼容
// 根据不同的provider类型和地图类型执行相应的匹配逻辑
func isLanguageCompatible(requestLang, tokenLang string, providerType dbproto.MapProviderEnum, mapType string) bool {
	// 向后兼容：空token语言匹配所有请求
	if tokenLang == "" {
		return true
	}

	// 卫星图跳过语言检查，因为卫星图通常不包含文字
	if mapType == MapTypeSatellite {
		return true
	}

	// 根据provider类型执行特定匹配逻辑
	switch providerType {
	case dbproto.MapProviderEnum_ProviderGoogleLocalDirectory:
		// Google支持多语言匹配
		return matchLanguage(requestLang, tokenLang)

	case dbproto.MapProviderEnum_ProviderTiandituLocalDirectory:
		// 天地图仅支持中文
		return matchTiandituLanguage(requestLang, tokenLang)

	case dbproto.MapProviderEnum_ProviderOSMLocalDirectory:
		// OSM跳过语言匹配，语言由地理位置决定
		return true

	default:
		// 未知provider类型，默认不兼容
		if config.IsVerboseDebugMap {
			slog.Warn("unknown provider type for language compatibility check",
				"providerType", providerType)
		}
		return false
	}
}
