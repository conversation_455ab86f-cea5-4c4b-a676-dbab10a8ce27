package maps

import (
	"bfmap/config"
	"bfmap/dbproto"
	"fmt"
	"log/slog"
	"os"
	"sync"
	"time"
)

// 文件存在性缓存，避免重复的文件系统调用
type fileExistenceCache struct {
	mu          sync.RWMutex
	cache       map[string]bool
	lastCleanup time.Time
}

var fileCache = &fileExistenceCache{
	cache:       make(map[string]bool),
	lastCleanup: time.Now(),
}

// 检查文件是否存在（带缓存）
func (c *fileExistenceCache) fileExists(filePath string) bool {
	c.mu.RLock()
	if exists, found := c.cache[filePath]; found {
		c.mu.RUnlock()
		return exists
	}
	c.mu.RUnlock()

	// 实际检查文件是否存在
	_, err := os.Stat(filePath)
	exists := err == nil

	// 缓存结果
	c.mu.Lock()
	c.cache[filePath] = exists
	// 只在缓存大小超过阈值时清理
	if len(c.cache) > 5000 {
		c.cleanup()
	}
	c.mu.Unlock()

	return exists
}

// 清理过期缓存
func (c *fileExistenceCache) cleanup() {
	if time.Since(c.lastCleanup) > 30*time.Minute {
		c.mu.Lock()
		c.cache = make(map[string]bool)
		c.lastCleanup = time.Now()
		c.mu.Unlock()
	}
}

// getLocalDirectoryProviderType 将在线provider类型映射为对应的本地目录provider类型
func getLocalDirectoryProviderType(onlineProviderType int) int {
	switch onlineProviderType {
	case int(dbproto.MapProviderEnum_ProviderGoogle):
		return int(dbproto.MapProviderEnum_ProviderGoogleLocalDirectory)
	case int(dbproto.MapProviderEnum_ProviderTianditu):
		return int(dbproto.MapProviderEnum_ProviderTiandituLocalDirectory)
	case int(dbproto.MapProviderEnum_ProviderOSM):
		return int(dbproto.MapProviderEnum_ProviderOSMLocalDirectory)
	default:
		// 不支持的provider类型
		return -1
	}
}

// filterCompatibleTokens 过滤出语言兼容的provider tokens
func filterCompatibleTokens(tokens []*MapProviderToken, mapReq *MapReq) []*MapProviderToken {
	var compatibleTokens []*MapProviderToken

	for _, token := range tokens {
		// 检查token基本有效性
		if !token.IsValid() {
			continue
		}

		// 检查zoom范围
		if token.MinZoom > 0 && mapReq.Z < token.MinZoom {
			continue
		}
		if token.MaxZoom > 0 && mapReq.Z > token.MaxZoom {
			continue
		}

		// 使用Language字段
		tokenLanguage := token.Language

		// 检查语言兼容性
		if isLanguageCompatible(mapReq.Lang, tokenLanguage, token.Provider, mapReq.MapType) {
			compatibleTokens = append(compatibleTokens, token)

			if config.IsVerboseDebugMap {
				slog.Debug("found language-compatible token",
					"tokenRid", token.TokenRid,
					"requestLang", mapReq.Lang,
					"tokenLang", tokenLanguage,
					"provider", token.Provider,
					"mapType", mapReq.MapType)
			}
		} else {
			if config.IsVerboseDebugMap {
				slog.Debug("token language not compatible",
					"tokenRid", token.TokenRid,
					"requestLang", mapReq.Lang,
					"tokenLang", tokenLanguage,
					"provider", token.Provider,
					"mapType", mapReq.MapType)
			}
		}
	}

	return compatibleTokens
}

// buildLocalFilePath 构建本地文件路径
func buildLocalFilePath(token *MapProviderToken, mapReq *MapReq) string {
	baseUrl := token.BaseUrl
	if baseUrl == "" {
		return ""
	}

	// 移除末尾的斜杠
	if baseUrl[len(baseUrl)-1] == '/' {
		baseUrl = baseUrl[:len(baseUrl)-1]
	}

	// 确定文件扩展名
	ext := "png"
	if mapReq.MapType == MapTypeSatellite || mapReq.MapType == MapTypeHybrid {
		ext = "jpg"
	}

	// 构建文件路径：{BaseUrl}/{mapType}/{z}/{x}/{y}.{ext}
	return fmt.Sprintf("%s/%s/%d/%d/%d.%s",
		baseUrl, mapReq.MapType, mapReq.Z, mapReq.X, mapReq.Y, ext)
}

// readLocalDirectoryFile 从本地目录读取文件
func readLocalDirectoryFile(token *MapProviderToken, mapReq *MapReq) ([]byte, error) {
	filePath := buildLocalFilePath(token, mapReq)
	if filePath == "" {
		return nil, fmt.Errorf("invalid file path")
	}

	// 读取文件
	imageBytes, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filePath, err)
	}

	if len(imageBytes) == 0 {
		return nil, fmt.Errorf("file %s is empty", filePath)
	}

	return imageBytes, nil
}

// QueryTileFromLocalDirectory 从本地目录查询地图瓦片
func QueryTileFromLocalDirectory(
	userRid string,
	mapReq *MapReq,
	localProviderType int,
	needCreateNewIndex bool,
) (tileInfo *TileInfo, imageBytes []byte, err error) {
	// 获取本地目录provider的tokens
	providerTokens, err := GlobalMapsManager.GetMapProviderTokens(userRid, localProviderType)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get local directory provider tokens: %w", err)
	}

	if len(providerTokens) == 0 {
		return nil, nil, fmt.Errorf("no local directory provider tokens found for provider type %d", localProviderType)
	}

	// 过滤出语言兼容的tokens
	compatibleTokens := filterCompatibleTokens(providerTokens, mapReq)
	if len(compatibleTokens) == 0 {
		return nil, nil, fmt.Errorf("no language-compatible local directory provider token found")
	}

	// 尝试从兼容的tokens中读取文件
	for _, token := range compatibleTokens {
		// 构建文件路径
		filePath := buildLocalFilePath(token, mapReq)
		if filePath == "" {
			if config.IsVerboseDebugMap {
				slog.Debug("invalid file path for token",
					"tokenRid", token.TokenRid,
					"baseUrl", token.BaseUrl)
			}
			continue
		}

		// 先检查文件是否存在（使用缓存）
		if !fileCache.fileExists(filePath) {
			if config.IsVerboseDebugMap {
				slog.Debug("local directory file does not exist",
					"filePath", filePath,
					"tokenRid", token.TokenRid)
			}
			continue
		}

		// 读取文件
		imageBytes, err = os.ReadFile(filePath)
		if err != nil {
			if config.IsVerboseDebugMap {
				slog.Debug("failed to read local directory file",
					"filePath", filePath,
					"tokenRid", token.TokenRid,
					"err", err)
			}
			// 更新缓存，标记文件不存在
			fileCache.mu.Lock()
			fileCache.cache[filePath] = false
			fileCache.mu.Unlock()
			continue
		}

		// 检查文件是否为空
		if len(imageBytes) == 0 {
			if config.IsVerboseDebugMap {
				slog.Debug("local directory file is empty",
					"filePath", filePath,
					"tokenRid", token.TokenRid)
			}
			continue
		}

		// 成功读取文件
		if config.IsVerboseDebugMap {
			slog.Debug("successfully read local directory file",
				"filePath", filePath,
				"tokenRid", token.TokenRid,
				"fileSize", len(imageBytes))
		}

		// 保存到数据库并创建TileInfo
		tileInfo, err = SaveTileToDb(mapReq, imageBytes, needCreateNewIndex)
		if err != nil {
			slog.Warn("QueryTileFromLocalDirectory SaveTileToDb fail", "req", mapReq.Key(), "err", err)
			// 即使保存失败，也返回图片数据
			tileInfo = &TileInfo{
				Hash:      "",
				CacheTime: 0,
				Status:    1,
			}
		}

		return tileInfo, imageBytes, nil
	}

	return nil, nil, fmt.Errorf("no local directory file found for request")
}
